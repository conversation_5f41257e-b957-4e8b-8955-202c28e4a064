@page "/menu"
@model PRN222_Restaurant.Pages.MenuModel
@{
    ViewData["Title"] = "Our Menu";
}

<!-- Hero Section -->
<section class="relative py-16 md:py-24">
    <div class="absolute inset-0 z-0">
        <img src="https://images.unsplash.com/photo-1551218808-94e220e084d2?q=80&w=2070" class="w-full h-full object-cover" />
        <div class="absolute inset-0 bg-gradient-to-b from-slate-900/80 to-slate-900/80"></div>
    </div>
    <div class="container mx-auto px-4 relative z-10 text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">Our Menu</h1>
        <p class="text-xl text-slate-200 max-w-3xl mx-auto">Explore our selection of fresh seafood dishes</p>
    </div>
</section>

<!-- Filter Form -->
<section class="bg-white py-8 sticky top-0 z-30 shadow-md">
    <div class="container mx-auto px-4">
        <form method="get" class="flex flex-col md:flex-row gap-4 items-center">
            <select name="CategoryId" class="select select-bordered">
                <option value="">Tất cả danh mục</option>
                @foreach (var c in Model.Categories)
                {
                    <option value="@c.Id" selected="@(Model.CategoryId == c.Id ? "selected" : null)">@c.Name</option>
                }
            </select>

            <select name="Status" class="select select-bordered">
                <option value="">Tất cả trạng thái</option>
                @foreach (var s in Model.MenuStatusDisplayNames)
                {
                    <option value="@s.Key" selected="@(Model.Status == s.Key ? "selected" : null)">@s.Value</option>
                }
            </select>

            <input name="SearchTerm" type="text" placeholder="Tìm món..." value="@Model.SearchTerm" class="input input-bordered" />
            <input name="MinPrice" type="number" placeholder="Giá từ" value="@Model.MinPrice" class="input input-bordered" />
            <input name="MaxPrice" type="number" placeholder="Giá đến" value="@Model.MaxPrice" class="input input-bordered" />

            <button type="submit" class="btn btn-primary">Lọc</button>
        </form>
    </div>
</section>

<!-- Menu Items -->
<section class="py-12 bg-slate-50">
    <div class="container mx-auto px-4">
        @if (Model.MenuItems.Count == 0)
        {
            <div class="text-center text-gray-500 py-8 text-lg font-semibold">Không tìm thấy kết quả phù hợp</div>
        }
        else
        {
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach (var item in Model.MenuItems)
                {
                    <div class="card bg-base-100 shadow-xl">
                        <figure class="h-48"><img src="@item.ImageUrl" alt="@item.Name" class="w-full h-full object-cover" /></figure>
                        <div class="card-body">
                            <h3 class="card-title text-xl">@item.Name</h3>
                            <p class="text-slate-600">@item.Description</p>
                            <div class="flex justify-between items-center mt-4">
                                <span class="text-xl font-bold">@item.Price.ToString("N0") VNĐ</span>
                                <span class="badge text-base font-semibold px-4 py-1 @(item.Status == Models.MenuItemStatus.Available ? "badge-success" : "badge-error text-white")">
                                    @(item.Status == Models.MenuItemStatus.Available ? "Còn món" : "Hết món")
                                </span>
                            </div>
                        </div>
                    </div>
                }
            </div>

            <!-- Pagination -->
            <div class="flex justify-center mt-8 gap-2">
                @for (int i = 1; i <= Model.TotalPages; i++)
                {
                    <a href="@Url.Page("/Menu", null, new {
                        CurrentPage = i,
                        CategoryId = Model.CategoryId,
                        Status = Model.Status,
                        SearchTerm = Model.SearchTerm,
                        MinPrice = Model.MinPrice,
                        MaxPrice = Model.MaxPrice
                }, null)"
                       class="btn @(i == Model.CurrentPage ? "btn-primary" : "btn-neutral")">
                        @i
                    </a>
                }
            </div>
        }
    </div>
</section>
