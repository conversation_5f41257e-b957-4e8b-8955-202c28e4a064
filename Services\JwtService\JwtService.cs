﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.IdentityModel.Tokens;
using PRN222_Restaurant.Models;

public class JwtService
{
    private readonly IConfiguration _config;

    public JwtService(IConfiguration config)
    {
        _config = config;
    }

    public string GenerateToken(User user)
    {
        var avatarUrl = "data:image/jpeg;base64,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";

        var claims = new[]
        {
        new Claim("UserId", user.Id.ToString()),
            new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new Claim(ClaimTypes.Name, user.FullName ?? ""),
            new Claim(ClaimTypes.Email, user.Email ?? ""),
            new Claim(ClaimTypes.Role, user.Role ?? ""),
            new Claim("avatar_url", avatarUrl)
    };

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_config["Jwt:Key"]!));
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var token = new JwtSecurityToken(
            issuer: _config["Jwt:Issuer"],
            audience: _config["Jwt:Audience"],
            claims: claims,
            expires: DateTime.Now.AddHours(1),
            signingCredentials: creds
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

}
