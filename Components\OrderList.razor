﻿@using PRN222_Restaurant.Models
@using System.Security.Claims
@inject IOrderService OrderService
@inject AuthenticationStateProvider AuthProvider
@inject NavigationManager Nav
@inject ILogger<OrderList> Logger

<h1 class="text-3xl font-bold text-center mb-8">Đơn hàng của tôi</h1>

@if (!string.IsNullOrEmpty(StatusMessage))
{
    <div class="alert @(StatusMessage.StartsWith("Error") ? "alert-error" : "alert-success") shadow-lg mb-4">
        <div>
            <span>@StatusMessage</span>
        </div>
    </div>
}

<div class="mb-4 flex flex-wrap gap-4 items-center">
    <label class="font-medium">Lọc theo trạng thái:</label>
    <select class="select select-bordered select-sm w-auto"
            @onchange="OnStatusChanged">
        <option value="">T<PERSON><PERSON> cả</option>
        @foreach (var status in AllStatuses)
        {
            <option value="@status" selected="@(status == SelectedStatus)">
                @GetStatusText(status)
            </option>
        }
    </select>
</div>

@if (IsLoading)
{
    <p class="text-center">Đang tải đơn hàng...</p>
}
else if (Orders == null || Orders.Count == 0)
{
    <p class="text-center text-gray-500">Không có đơn hàng nào.</p>
}
else
{
    <div class="overflow-x-auto">
        <table class="table w-full">
            <thead>
                <tr>
                    <th>Mã</th><th>Loại</th><th>Bàn</th><th>Ngày</th><th>Giờ đặt</th><th>Tổng</th><th>Trạng thái</th><th>Chi tiết</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var order in Orders)
                {
                    var a = $"/order-detail/{order.Id}";
                    <tr>
                        <td>#@order.Id</td>
                        <td>
                            <span class="badge @(order.OrderType == "Immediate" ? "badge-info" : "badge-warning")">
                                @(order.OrderType == "Immediate" ? "Tại chỗ" : "Đặt trước")
                            </span>
                        </td>
                        <td>@(order.Table?.TableNumber != null ? $"Bàn {order.Table.TableNumber}" : "N/A")</td>
                        <td>@order.OrderDate.ToString("dd/MM/yyyy HH:mm")</td>
                        <td>@(order.ReservationTime?.ToString("dd/MM/yyyy HH:mm") ?? "N/A")</td>
                        <td>@order.TotalPrice.ToString("N0") đ</td>
                        <td>
                            <span class="badge @GetStatusClass(order.Status)">
                                @GetStatusText(order.Status)
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-info"
                                    @onclick="() => Nav.NavigateTo(a, forceLoad: true)">
                                Chi tiết
                            </button>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="flex justify-between items-center mt-4">
        <div class="text-sm text-gray-600">
            Hiển thị @((CurrentPage - 1) * PageSize + 1) đến
            @(Math.Min(CurrentPage * PageSize, TotalCount))
            trên tổng số @TotalCount đơn hàng
        </div>

        <div class="join">
            <button class="btn btn-outline" @onclick="() => ChangePage(CurrentPage - 1)" disabled="@(CurrentPage <= 1)">«</button>
            @for (int i = Math.Max(1, CurrentPage - 2); i <= Math.Min(TotalPages, CurrentPage + 2); i++)
            {
                int pageNumber = i; //Tách riêng biến để tránh lỗi capture
                <button class="btn @(pageNumber == CurrentPage ? "btn-active" : "btn-outline")"
                        @onclick="async () => await ChangePage(pageNumber)">
                    @pageNumber
                </button>
            }
            <button class="btn btn-outline" @onclick="() => ChangePage(CurrentPage + 1)" disabled="@(CurrentPage >= TotalPages)">»</button>
        </div>

        <div>
            <select class="select select-bordered select-sm"
                    @onchange="OnPageSizeChanged">
                @foreach (var size in new[] { 5, 10, 20, 50 })
                {
                    <option value="@size" selected="@(PageSize == size)">
                        @size
                    </option>
                }
            </select>
        </div>
    </div>
}

@code {
    private List<Order> Orders = new();
    private bool IsLoading = true;
    private int CurrentUserId;
    private int CurrentPage = 1;
    private int PageSize = 10;
    private int TotalCount = 0;
    private string? SelectedStatus;
    private string? StatusMessage;

    private readonly List<string> AllStatuses = new() { "Pending", "Preparing", "Served", "Completed", "Cancelled" };

    protected override async Task OnInitializedAsync()
    {
        await GetUserIdAsync();
        await LoadOrders();
    }

    private async Task GetUserIdAsync()
    {
        var state = await AuthProvider.GetAuthenticationStateAsync();
        var user = state.User;
        if (user.Identity?.IsAuthenticated == true)
        {
            int.TryParse(user.FindFirst(ClaimTypes.NameIdentifier)?.Value, out CurrentUserId);
        }
    }

    private async Task LoadOrders()
    {
        if (CurrentUserId <= 0) return;

        IsLoading = true;

        var result = await OrderService.GetPagedUserOrdersAsync(CurrentUserId, CurrentPage, PageSize, SelectedStatus);
        Orders = result.Items;
        TotalCount = result.TotalCount;
        IsLoading = false;
    }

    private int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

    private async Task ChangePage(int page)
    {
        if (page < 1 || page > TotalPages) return;

        if (page != CurrentPage)
        {
            CurrentPage = page;
            await LoadOrders();
            StateHasChanged(); // đảm bảo UI cập nhật lại
        }
    }

    private async Task OnStatusChanged(ChangeEventArgs e)
    {
        SelectedStatus = e.Value?.ToString();
        CurrentPage = 1;
        await LoadOrders();
    }

    private async Task OnPageSizeChanged(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out var size))
        {
            PageSize = size;
            CurrentPage = 1;
            await LoadOrders();
        }
    }

    private string GetStatusText(string status) => status switch
    {
        "Pending" => "Chờ xử lý",
        "Preparing" => "Đang chuẩn bị",
        "Served" => "Đã phục vụ",
        "Completed" => "Hoàn thành",
        "Cancelled" => "Đã hủy",
        _ => status
    };

    private string GetStatusClass(string status) => status switch
    {
        "Pending" => "badge-warning",
        "Preparing" => "badge-info",
        "Served" => "badge-success",
        "Completed" => "badge-success",
        "Cancelled" => "badge-error",
        _ => "badge"
    };
}