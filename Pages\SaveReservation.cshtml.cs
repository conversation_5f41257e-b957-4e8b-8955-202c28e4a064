using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using PRN222_Restaurant.Services.IService;

namespace PRN222_Restaurant.Pages
{
    public class SaveReservationModel : PageModel
    {
        private readonly IReservationSessionService _reservationSessionService;

        public SaveReservationModel(IReservationSessionService reservationSessionService)
        {
            _reservationSessionService = reservationSessionService;
        }

        public IActionResult OnPost(int tableId, DateTime reservationDate, TimeSpan reservationTime,
            int numberOfGuests, string? note, string? selectedItems)
        {
            try
            {
                Console.WriteLine($"SaveReservation OnPost called with tableId: {tableId}, date: {reservationDate}, time: {reservationTime}");

                // Save reservation data to session
                var reservationData = new ReservationSessionData
                {
                    TableId = tableId,
                    ReservationDate = reservationDate,
                    ReservationTime = reservationTime,
                    NumberOfGuests = numberOfGuests,
                    Note = note,
                    SelectedItems = selectedItems
                };

                _reservationSessionService.SaveReservationData(reservationData);
                Console.WriteLine("Reservation data saved successfully");

                // Redirect to login page
                Console.WriteLine("Redirecting to /login");
                return Redirect("/login");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in SaveReservation: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return Redirect("/login"); // Still redirect to login even on error
            }
        }
    }
}
