using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using PRN222_Restaurant.Services.IService;

namespace PRN222_Restaurant.Pages
{
    public class SaveReservationModel : PageModel
    {
        private readonly IReservationSessionService _reservationSessionService;

        public SaveReservationModel(IReservationSessionService reservationSessionService)
        {
            _reservationSessionService = reservationSessionService;
        }

        public IActionResult OnPost(int tableId, DateTime reservationDate, TimeSpan reservationTime, 
            int numberOfGuests, string note, string selectedItems)
        {
            // Save reservation data to session
            _reservationSessionService.SaveReservationData(tableId, reservationDate, reservationTime, 
                numberOfGuests, note ?? string.Empty, selectedItems ?? string.Empty);
            
            // Redirect to home page for login
            return Redirect("/");
        }
    }
}
