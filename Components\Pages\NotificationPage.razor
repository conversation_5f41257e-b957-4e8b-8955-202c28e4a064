@page "/notifications-blazor"
@using PRN222_Restaurant.Services.IService
@using PRN222_Restaurant.Models
@using System.Security.Claims
@inject INotificationService NotificationService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject ILogger<NotificationPage> Logger
@inject NavigationManager Navigation

<PageTitle>Thông báo</PageTitle>

<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        @* Test button for development - commented out for production *@
        @* <div class="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p class="text-sm text-yellow-800 mb-2">Development Tools:</p>
            <button class="btn btn-sm btn-warning" @onclick="CreateTestNotifications">
                Tạo Test Notifications (15 items)
            </button>
        </div>

        <!-- Simple Test Component -->
        <div class="mb-8">
            <h2 class="text-xl font-bold mb-4">Simple Test Component</h2>
            <SimpleNotificationTest />
        </div>

        <hr class="my-8" /> *@

        <!-- NotificationList Component -->
        <div>
            <NotificationList />
        </div>
    </div>
</div>

@code {
    private int currentUserId = 0;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Logger.LogInformation("NotificationPage: OnInitializedAsync started");
            await GetCurrentUserId();
            // Logger.LogInformation($"NotificationPage: Current user ID: {currentUserId}");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "NotificationPage: Error in OnInitializedAsync");
        }
    }

    private async Task GetCurrentUserId()
    {
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            // Logger.LogInformation($"NotificationPage: User authenticated: {user.Identity?.IsAuthenticated}");

            if (user.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                // Logger.LogInformation($"NotificationPage: User ID claim: {userIdClaim}");

                if (int.TryParse(userIdClaim, out int userId))
                {
                    currentUserId = userId;
                    // Logger.LogInformation($"NotificationPage: Successfully parsed user ID: {currentUserId}");
                }
                else
                {
                    Logger.LogWarning($"NotificationPage: Failed to parse user ID from claim: {userIdClaim}");
                }
            }
            else
            {
                Logger.LogWarning("NotificationPage: User is not authenticated");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "NotificationPage: Error in GetCurrentUserId");
        }
    }

    // Test method - commented out for production
    /*
    private async Task CreateTestNotifications()
    {
        if (currentUserId <= 0)
        {
            Logger.LogWarning("NotificationPage: Cannot create test notifications - no valid user ID");
            return;
        }

        try
        {
            Logger.LogInformation($"NotificationPage: Creating test notifications for user {currentUserId}");

            // Create test notifications
            for (int i = 1; i <= 15; i++)
            {
                await NotificationService.AddAsync(new Models.Notification
                {
                    UserId = currentUserId,
                    Title = $"Test Notification {i}",
                    Message = $"This is test notification number {i} for testing pagination and mark as read functionality.",
                    Type = i % 4 == 0 ? "Success" : i % 3 == 0 ? "Warning" : i % 2 == 0 ? "Error" : "Info",
                    CreatedAt = DateTime.Now.AddMinutes(-i * 5),
                    IsRead = i % 5 == 0, // Some notifications are already read
                    RelatedUrl = i % 3 == 0 ? "/order-list" : "#"
                });
            }

            Logger.LogInformation("NotificationPage: Test notifications created successfully");
            StateHasChanged(); // Refresh the page
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "NotificationPage: Error creating test notifications");
        }
    }
    */
}
