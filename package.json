{"name": "prn222_restaurant", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"watch": "npx @tailwindcss/cli -i wwwroot/css/site.css -o wwwroot/css/site.min.css --watch"}, "repository": {"type": "git", "url": "git+https://github.com/Lain4504/PRN222-Restaurant.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Lain4504/PRN222-Restaurant/issues"}, "homepage": "https://github.com/Lain4504/PRN222-Restaurant#readme", "devDependencies": {"@tailwindcss/cli": "^4.1.7", "autoprefixer": "^10.4.17", "daisyui": "^5.0.35", "postcss": "^8.4.35", "tailwindcss": "^4.1.7"}, "dependencies": {"alpinejs": "^3.14.9", "ansi-regex": "^6.1.0", "ansi-styles": "^6.2.1", "any-promise": "^1.3.0", "anymatch": "^3.1.3", "arg": "^5.0.2", "balanced-match": "^1.0.2", "binary-extensions": "^2.3.0", "brace-expansion": "^2.0.1", "braces": "^3.0.3", "browserslist": "^4.24.5", "camelcase-css": "^2.0.1", "caniuse-lite": "^1.0.30001718", "chalk": "^4.1.2", "chokidar": "^3.6.0", "cliui": "^8.0.1", "color-convert": "^2.0.1", "color-name": "^1.1.4", "commander": "^4.1.1", "concurrently": "^9.1.2", "cross-spawn": "^7.0.6", "cssesc": "^3.0.0", "didyoumean": "^1.2.2", "dlv": "^1.1.3", "eastasianwidth": "^0.2.0", "electron-to-chromium": "^1.5.152", "emoji-regex": "^9.2.2", "esbuild": "^0.25.4", "escalade": "^3.2.0", "fast-glob": "^3.3.3", "fastq": "^1.19.1", "fill-range": "^7.1.1", "foreground-child": "^3.3.1", "fraction.js": "^4.3.7", "function-bind": "^1.1.2", "get-caller-file": "^2.0.5", "glob": "^10.4.5", "glob-parent": "^6.0.2", "has-flag": "^4.0.0", "hasown": "^2.0.2", "is-binary-path": "^2.1.0", "is-core-module": "^2.16.1", "is-extglob": "^2.1.1", "is-fullwidth-code-point": "^3.0.0", "is-glob": "^4.0.3", "is-number": "^7.0.0", "isexe": "^2.0.0", "jackspeak": "^3.4.3", "jiti": "^1.21.7", "lilconfig": "^3.1.3", "lines-and-columns": "^1.2.4", "lodash": "^4.17.21", "lodash.castarray": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.merge": "^4.6.2", "lru-cache": "^10.4.3", "merge2": "^1.4.1", "micromatch": "^4.0.8", "mini-svg-data-uri": "^1.4.4", "minimatch": "^9.0.5", "minipass": "^7.1.2", "mz": "^2.7.0", "nanoid": "^3.3.11", "node-releases": "^2.0.19", "normalize-path": "^3.0.0", "normalize-range": "^0.1.2", "object-assign": "^4.1.1", "object-hash": "^3.0.0", "package-json-from-dist": "^1.0.1", "path-key": "^3.1.1", "path-parse": "^1.0.7", "path-scurry": "^1.11.1", "picocolors": "^1.1.1", "picomatch": "^2.3.1", "pify": "^2.3.0", "pirates": "^4.0.7", "postcss-import": "^15.1.0", "postcss-js": "^4.0.1", "postcss-load-config": "^4.0.2", "postcss-nested": "^6.2.0", "postcss-selector-parser": "^6.1.2", "postcss-value-parser": "^4.2.0", "queue-microtask": "^1.2.3", "read-cache": "^1.0.0", "readdirp": "^3.6.0", "require-directory": "^2.1.1", "resolve": "^1.22.10", "reusify": "^1.1.0", "run-parallel": "^1.2.0", "rxjs": "^7.8.2", "shebang-command": "^2.0.0", "shebang-regex": "^3.0.0", "shell-quote": "^1.8.2", "signal-exit": "^4.1.0", "source-map-js": "^1.2.1", "string-width": "^5.1.2", "string-width-cjs": "^4.2.3", "strip-ansi": "^7.1.0", "strip-ansi-cjs": "^6.0.1", "sucrase": "^3.35.0", "supports-color": "^8.1.1", "supports-preserve-symlinks-flag": "^1.0.0", "thenify": "^3.3.1", "thenify-all": "^1.6.0", "to-regex-range": "^5.0.1", "tree-kill": "^1.2.2", "ts-interface-checker": "^0.1.13", "tslib": "^2.8.1", "update-browserslist-db": "^1.1.3", "util-deprecate": "^1.0.2", "which": "^2.0.2", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "^7.0.0", "y18n": "^5.0.8", "yaml": "^2.7.1", "yargs": "^17.7.2", "yargs-parser": "^21.1.1"}}