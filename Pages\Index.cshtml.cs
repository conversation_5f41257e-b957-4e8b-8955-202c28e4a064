using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;
using PRN222_Restaurant.Models;
using PRN222_Restaurant.Services;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PRN222_Restaurant.Pages
{
    public class IndexModel : PageModel
    {
        private readonly IFeedbackService _feedbackService;
        private readonly ILogger<IndexModel> _logger;

        public IndexModel(IFeedbackService feedbackService, ILogger<IndexModel> logger)
        {
            _feedbackService = feedbackService;
            _logger = logger;
        }

        public List<Feedback> Feedbacks { get; set; } = new();

        public async Task<IActionResult> OnGetAsync()
        {
            // Check if user just logged in and has saved reservation data
            if (User.Identity.IsAuthenticated)
            {
                var savedData = _reservationSessionService.GetReservationData();
                if (savedData != null)
                {
                    // Redirect to reservation page which will handle the saved data
                    return RedirectToPage("/Reservation");
                }
            }

            var allFeedbacks = await _feedbackService.GetAllAsync();

            Feedbacks = allFeedbacks
                .OrderByDescending(f => f.CreatedAt)
                .ToList();

            return Page();
        }

    }

}