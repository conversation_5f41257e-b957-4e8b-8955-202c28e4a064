@page "/admin/dashboard"
@model PRN222_Restaurant.Pages.Admin.DashboardModel
@{
    ViewData["Title"] = "Dashboard";
}

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
    <!-- Stats Cards -->
    <div class="card bg-base-100 shadow-md">
        <div class="card-body p-4">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="card-title text-lg">Total Users</h2>
                    <p class="text-3xl font-bold">@Model.TotalUsers</p>
                </div>
                <div class="bg-primary/20 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-100 shadow-md">
        <div class="card-body p-4">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="card-title text-lg">Total Products</h2>
                    <p class="text-3xl font-bold">@Model.TotalProducts</p>
                </div>
                <div class="bg-secondary/20 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-100 shadow-md">
        <div class="card-body p-4">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="card-title text-lg">Total Orders</h2>
                    <p class="text-3xl font-bold">@Model.TotalOrders</p>
                </div>
                <div class="bg-accent/20 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-100 shadow-md">
        <div class="card-body p-4">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="card-title text-lg">Revenue</h2>
                    <p class="text-3xl font-bold">$@Model.TotalRevenue</p>
                </div>
                <div class="bg-success/20 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Orders Section -->
<div class="card bg-base-100 shadow-md mb-8">
    <div class="card-body">
        <h2 class="card-title text-xl mb-4">Recent Orders</h2>
        
        <div class="overflow-x-auto">
            <table class="table table-zebra w-full">
                <thead>
                    <tr>
                        <th>Order ID</th>
                        <th>Customer</th>
                        <th>Status</th>
                        <th>Total</th>
                        <th>Date</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var order in Model.RecentOrders)
                    {
                        <tr>
                            <td>#@order.Id</td>
                            <td>@order.CustomerName</td>
                            <td>
                                @if (order.Status == "Completed")
                                {
                                    <span class="badge badge-success">@order.Status</span>
                                }
                                else if (order.Status == "Pending")
                                {
                                    <span class="badge badge-warning">@order.Status</span>
                                }
                                else
                                {
                                    <span class="badge badge-info">@order.Status</span>
                                }
                            </td>
                            <td>$@order.Total.ToString("0.00")</td>
                            <td>@order.Date.ToString("dd/MM/yyyy")</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Quick Actions and Info Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div class="card bg-base-100 shadow-md">
        <div class="card-body">            <h2 class="card-title text-xl mb-4">Quick Actions</h2>
            <div class="flex flex-wrap gap-2">
                <a href="/admin/users/create" class="btn btn-primary">Add New User</a>
                <a href="/admin/products/create" class="btn btn-secondary">Add New Product</a>
                <a href="/admin/orders" class="btn btn-accent">View All Orders</a>
                <a href="/admin/stats" class="btn btn-info">View Statistics</a>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-100 shadow-md">
        <div class="card-body">
            <h2 class="card-title text-xl mb-4">System Info</h2>
            <ul class="space-y-2">
                <li>
                    <div class="flex justify-between">
                        <span>Server Status:</span>
                        <span class="text-success font-medium">Online</span>
                    </div>
                </li>
                <li>
                    <div class="flex justify-between">
                        <span>Database Status:</span>
                        <span class="text-success font-medium">Connected</span>
                    </div>
                </li>
                <li>
                    <div class="flex justify-between">
                        <span>Last Backup:</span>
                        <span>@DateTime.Now.AddDays(-1).ToString("dd/MM/yyyy HH:mm")</span>
                    </div>
                </li>
                <li>
                    <div class="flex justify-between">
                        <span>System Version:</span>
                        <span>1.0.0</span>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Show welcome toast
        document.addEventListener('DOMContentLoaded', function() {
            showToast('Welcome back, Admin!', 'info', 3000);
        });
    </script>
}
