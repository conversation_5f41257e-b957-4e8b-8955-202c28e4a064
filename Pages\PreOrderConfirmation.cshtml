@page "/preorderconfirmation/{id:int}"
@model PRN222_Restaurant.Pages.PreOrderConfirmationModel
@{
    ViewData["Title"] = "Reservation Confirmation";
}

<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body p-8">
                    <!-- Success Message -->
                    <div class="flex flex-col items-center justify-center py-4 mb-8">
                        <div class="text-center mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-success mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h1 class="text-3xl font-bold mb-2 text-center">Pre-Order Successfully Placed!</h1>
                        <p class="text-center text-gray-600 mb-6">
                            Thank you for choosing Ocean Delights. Your reservation and pre-order have been successfully booked.
                        </p>
                    </div>
                    
                    <!-- Payment Timer Alert -->
                    <div class="alert alert-warning mb-8">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <div>
                                <h3 class="font-bold">Please complete your payment within:</h3>
                                <div class="text-lg font-mono font-bold" id="countdown">15:00</div>
                                <p class="text-sm">Your reservation will be automatically cancelled if payment is not completed within 15 minutes.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Reservation Details -->
                    <div class="bg-base-200 p-6 rounded-lg mb-8">
                        <h2 class="text-xl font-semibold mb-4">Reservation Details</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p><span class="font-medium">Date:</span> @Model.ReservationDate.ToString("MMMM dd, yyyy")</p>
                                <p><span class="font-medium">Time:</span> @Model.ReservationTime.ToString("hh\\:mm")</p>
                            </div>
                            <div>
                                <p><span class="font-medium">Table:</span> @Model.TableNumber</p>
                                <p><span class="font-medium">Guests:</span> @Model.NumberOfGuests</p>
                            </div>
                        </div>
                        @if (!string.IsNullOrEmpty(Model.Note))
                        {
                            <div class="mt-4">
                                <p><span class="font-medium">Special Requests:</span></p>
                                <p class="italic">@Model.Note</p>
                            </div>
                        }
                    </div>

                    <!-- Pre-Order Items -->
                    @if (Model.OrderItems.Any())
                    {
                        <div class="mb-8">
                            <h2 class="text-xl font-semibold mb-4">Pre-Ordered Items</h2>
                            <div class="overflow-x-auto">
                                <table class="table w-full">
                                    <thead>
                                        <tr>
                                            <th>Item</th>
                                            <th class="text-right">Quantity</th>
                                            <th class="text-right">Price</th>
                                            <th class="text-right">Subtotal</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var item in Model.OrderItems)
                                        {
                                            <tr>
                                                <td>@item.Name</td>
                                                <td class="text-right">@item.Quantity</td>
                                                <td class="text-right">$@item.Price.ToString("F2")</td>
                                                <td class="text-right">$@item.Subtotal.ToString("F2")</td>
                                            </tr>
                                        }
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="3" class="text-right font-bold">Total:</td>
                                            <td class="text-right font-bold">$@Model.TotalAmount.ToString("F2")</td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    }
                    
                    <!-- Actions -->
                    <div class="flex flex-col items-center gap-4 mt-8">
                        <a href="/checkout?orderId=@Model.Order.Id" class="btn btn-primary btn-lg w-full md:w-1/2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            </svg>
                            Pay Now
                        </a>
                        <div class="flex flex-col md:flex-row gap-4 justify-center w-full">
                            <a href="/" class="btn btn-outline flex-1">Return to Homepage</a>
                            <a href="/menu" class="btn btn-outline flex-1">View Our Menu</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Countdown timer functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Set the countdown time (15 minutes in seconds)
            let timeLeft = 15 * 60;
            const countdownElement = document.getElementById('countdown');
            
            // Update the countdown every second
            const countdownInterval = setInterval(function() {
                // Calculate minutes and seconds
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                
                // Display the time with leading zeros if needed
                countdownElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                // Decrement the time
                timeLeft--;
                
                // Check if time has expired
                if (timeLeft < 0) {
                    clearInterval(countdownInterval);
                    countdownElement.textContent = '00:00';
                    countdownElement.classList.add('text-red-500');
                    
                    // You could redirect to a payment failed page or show a message
                    alert('Your reservation time has expired. The order will be cancelled.');
                    window.location.href = '/';
                }
                
                // Change color to red when less than 3 minutes remaining
                if (timeLeft < 180) {
                    countdownElement.classList.add('text-red-500');
                }
            }, 1000);
        });
    </script>
} 