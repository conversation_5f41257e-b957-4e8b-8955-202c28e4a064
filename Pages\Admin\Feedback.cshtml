﻿@page "/admin/feedback"
@model PRN222_Restaurant.Pages.Admin.FeedbackModel

<h2 class="text-2xl font-bold mb-4"><PERSON><PERSON> sách Feedback</h2>

<table class="table w-full">
    <thead>
        <tr>
            <th>ID</th>
            <th>Người dùng</th>
            <th><PERSON>hận xét</th>
            <th><PERSON><PERSON>h giá</th>
            <th><PERSON><PERSON><PERSON> tạo</th>
            <th><PERSON>ành động</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var fb in Model.Feedbacks)
        {
            <tr>
                <td>@fb.Id</td>
                <td>@fb.User.FullName</td>
                <td>@fb.Comment</td>
                <td>@fb.Rating</td>
                <td>@fb.CreatedAt.ToString("dd/MM/yyyy")</td>
                <td>
                    <form method="post" asp-page-handler="Delete" asp-route-id="@fb.Id" class="inline-block" onsubmit="return confirm('<PERSON>ạn có chắc muốn xóa phản hồi này?');">
                        <button type="submit" class="btn btn-xs btn-error">Xóa</button>
                    </form>
                </td>
            </tr>
        }
    </tbody>
</table>

<!-- Pagination controls -->
<div class="flex justify-between items-center mt-4">
    <div class="text-sm text-gray-600">
        Hiển thị @((Model.FeedbackResult.Page - 1) * Model.FeedbackResult.PageSize + 1) đến
        @(Math.Min(Model.FeedbackResult.Page * Model.FeedbackResult.PageSize, Model.FeedbackResult.TotalCount))
        trên tổng số @Model.FeedbackResult.TotalCount phản hồi
    </div>

    <div class="join">
        @if (Model.FeedbackResult.HasPreviousPage)
        {
            <a asp-page="/Admin/Feedback"
               asp-route-currentPage="@(Model.FeedbackResult.Page - 1)"
               asp-route-pageSize="@Model.FeedbackResult.PageSize"
               class="join-item btn btn-outline">«</a>
        }
        else
        {
            <button class="join-item btn btn-outline" disabled>«</button>
        }

        @for (int i = Math.Max(1, Model.FeedbackResult.Page - 2); i <= Math.Min(Model.FeedbackResult.TotalPages, Math.Max(Model.FeedbackResult.Page + 2, 5)); i++)
        {
            if (i == Model.FeedbackResult.Page)
            {
                <button class="join-item btn btn-active">@i</button>
            }
            else
            {
                <a asp-page="/Admin/Feedback"
                   asp-route-currentPage="@i"
                   asp-route-pageSize="@Model.FeedbackResult.PageSize"
                   class="join-item btn btn-outline">@i</a>
            }
        }

        @if (Model.FeedbackResult.HasNextPage)
        {
            <a asp-page="/Admin/Feedback"
               asp-route-currentPage="@(Model.FeedbackResult.Page + 1)"
               asp-route-pageSize="@Model.FeedbackResult.PageSize"
               class="join-item btn btn-outline">»</a>
        }
        else
        {
            <button class="join-item btn btn-outline" disabled>»</button>
        }
    </div>

    <div class="flex items-center">
        <span class="mr-2">Hiển thị:</span>
        <select id="pageSize"
                class="select select-bordered select-sm w-auto max-w-xs"
                onchange="location.href='@Url.Page("/Admin/Feedback", new { currentPage = 1 })&pageSize=' + this.value">
            @{
                int currentSize = Model.FeedbackResult.PageSize;
            }
            <option value="5" selected="@(currentSize == 5)">5</option>
            <option value="10" selected="@(currentSize == 10)">10</option>
            <option value="20" selected="@(currentSize == 20)">20</option>
            <option value="50" selected="@(currentSize == 50)">50</option>
        </select>
    </div>
</div>
