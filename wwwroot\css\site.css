@import "tailwindcss";
@plugin "daisyui";

/* Custom styles for the admin dashboard */
.fade-out {
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

/* Loading skeleton animation */
@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

.animate-pulse {
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  background-size: 1000px 100%;
  animation: shimmer 2s infinite;
}

/* Active sidebar item */
.menu .active {
  background-color: hsl(var(--p) / 0.2);
  color: hsl(var(--pc));
}

/* Table responsive styles */
@media (max-width: 768px) {
  .table-responsive th:not(:first-child):not(:last-child),
  .table-responsive td:not(:first-child):not(:last-child) {
    display: none;
  }
}

/* Card hover effects */
.card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}