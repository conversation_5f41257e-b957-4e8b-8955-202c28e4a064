@page "/admin/stats"
@model PRN222_Restaurant.Pages.Admin.StatsModel
@{
    ViewData["Title"] = "Thống kê";
}

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
    <!-- Stats Cards -->
    <div class="card bg-base-100 shadow-md">
        <div class="card-body p-4">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="card-title text-lg">Tổng doanh thu</h2>
                    <p class="text-3xl font-bold">@Model.TotalRevenue.ToString("N0")đ</p>
                    <p class="text-sm text-success mt-1">
                        <span class="inline-flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586l3.293-3.293A1 1 0 0114 7z" clip-rule="evenodd" />
                            </svg>
                            12.5% so với tháng trước
                        </span>
                    </p>
                </div>
                <div class="bg-primary/20 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-100 shadow-md">
        <div class="card-body p-4">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="card-title text-lg">Tổng đơn hàng</h2>
                    <p class="text-3xl font-bold">@Model.TotalOrders</p>
                    <p class="text-sm text-success mt-1">
                        <span class="inline-flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586l3.293-3.293A1 1 0 0114 7z" clip-rule="evenodd" />
                            </svg>
                            8.2% so với tháng trước
                        </span>
                    </p>
                </div>
                <div class="bg-secondary/20 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-100 shadow-md">
        <div class="card-body p-4">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="card-title text-lg">Khách hàng mới</h2>
                    <p class="text-3xl font-bold">@Model.NewCustomers</p>
                    <p class="text-sm text-error mt-1">
                        <span class="inline-flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M16 7a1 1 0 11-2 0V5H9a1 1 0 010-2h5a1 1 0 011 1v3zm-9 9a1 1 0 110 2H5a1 1 0 01-1-1v-3a1 1 0 112 0v1h3a1 1 0 110 2z" clip-rule="evenodd" />
                                <path fill-rule="evenodd" d="M12.707 11.293a1 1 0 010 1.414L9.414 16H11a1 1 0 110 2H7a1 1 0 01-1-1v-4a1 1 0 112 0v1.586l3.293-3.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                            3.1% so với tháng trước
                        </span>
                    </p>
                </div>
                <div class="bg-accent/20 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card bg-base-100 shadow-md">
        <div class="card-body p-4">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="card-title text-lg">Sản phẩm bán chạy</h2>
                    <p class="text-xl font-bold truncate">@Model.TopSellingProduct</p>
                    <p class="text-sm text-info mt-1">@Model.TopSellingCount đơn hàng</p>
                </div>
                <div class="bg-success/20 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Sales by Category Chart -->
    <div class="card bg-base-100 shadow-md lg:col-span-2">
        <div class="card-body">
            <h2 class="card-title text-xl mb-6">Doanh thu theo tháng</h2>
            
            <!-- Chart will be inserted here by JavaScript -->
            <div id="monthlySalesChart" class="w-full h-80"></div>
        </div>
    </div>
    
    <!-- Sales by Category -->
    <div class="card bg-base-100 shadow-md">
        <div class="card-body">
            <h2 class="card-title text-xl mb-6">Doanh thu theo danh mục</h2>
            
            <!-- Pie chart will be inserted here by JavaScript -->
            <div id="categorySalesChart" class="w-full h-80"></div>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Top Selling Products -->
    <div class="card bg-base-100 shadow-md">
        <div class="card-body">
            <h2 class="card-title text-xl mb-6">Sản phẩm bán chạy</h2>
            <div class="overflow-x-auto">
                <table class="table table-zebra w-full">
                    <thead>
                        <tr>
                            <th>Sản phẩm</th>
                            <th>Danh mục</th>
                            <th>Số lượng</th>
                            <th>Doanh thu</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var product in Model.TopProducts)
                        {
                            <tr>
                                <td>
                                    <div class="flex items-center gap-3">
                                        <div class="avatar">
                                            <div class="mask mask-squircle w-8 h-8">
                                                <img src="@product.ImageUrl" alt="@product.Name" />
                                            </div>
                                        </div>
                                        <div class="font-bold">@product.Name</div>
                                    </div>
                                </td>
                                <td>@product.Category</td>
                                <td>@product.QuantitySold</td>
                                <td>@product.Revenue.ToString("N0")đ</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Recent Customer Reviews -->
    <div class="card bg-base-100 shadow-md">
        <div class="card-body">
            <h2 class="card-title text-xl mb-6">Đánh giá gần đây</h2>
            
            <div class="space-y-4">
                @foreach (var review in Model.RecentReviews)
                {
                    <div class="bg-base-200 p-4 rounded-lg">
                        <div class="flex justify-between items-start">
                            <div class="flex items-center gap-3">
                                <div class="avatar">
                                    <div class="mask mask-squircle w-10 h-10">
                                        <img src="https://ui-avatars.com/api/?name=@Uri.EscapeDataString(review.CustomerName)&background=random" alt="@review.CustomerName" />
                                    </div>
                                </div>
                                <div>
                                    <p class="font-medium">@review.CustomerName</p>
                                    <div class="rating rating-xs">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            <input type="radio" name="<EMAIL>" class="mask mask-star-2 bg-orange-400" disabled @(i == review.Rating ? "checked" : "") />
                                        }
                                    </div>
                                </div>
                            </div>
                            <span class="text-sm text-gray-500">@review.Date.ToString("dd/MM/yyyy")</span>
                        </div>
                        <p class="mt-2 text-sm">@review.Comment</p>
                        <p class="text-xs text-gray-500 mt-1">Sản phẩm: @review.ProductName</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <!-- Include ApexCharts library -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Monthly Sales Chart
            var monthlySalesOptions = {
                series: [{
                    name: 'Doanh thu',
                    data: @Html.Raw(Json.Serialize(Model.MonthlySalesData))
                }],
                chart: {
                    height: 350,
                    type: 'area',
                    toolbar: {
                        show: false
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth'
                },
                xaxis: {
                    categories: ['Th1', 'Th2', 'Th3', 'Th4', 'Th5', 'Th6', 'Th7', 'Th8', 'Th9', 'Th10', 'Th11', 'Th12']
                },
                tooltip: {
                    y: {
                        formatter: function(val) {
                            return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") + "đ";
                        }
                    }
                }
            };

            var monthlySalesChart = new ApexCharts(document.querySelector("#monthlySalesChart"), monthlySalesOptions);
            monthlySalesChart.render();

            // Category Sales Chart
            var categorySalesOptions = {
                series: @Html.Raw(Json.Serialize(Model.CategorySalesData.Select(c => c.Value).ToArray())),
                chart: {
                    type: 'donut',
                    height: 350
                },
                labels: @Html.Raw(Json.Serialize(Model.CategorySalesData.Select(c => c.Key).ToArray())),
                responsive: [{
                    breakpoint: 480,
                    options: {
                        chart: {
                            width: 300
                        },
                        legend: {
                            position: 'bottom'
                        }
                    }
                }],
                tooltip: {
                    y: {
                        formatter: function(val) {
                            return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") + "đ";
                        }
                    }
                }
            };

            var categorySalesChart = new ApexCharts(document.querySelector("#categorySalesChart"), categorySalesOptions);
            categorySalesChart.render();
        });
    </script>
}
