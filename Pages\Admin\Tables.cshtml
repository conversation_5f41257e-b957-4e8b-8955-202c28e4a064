@page "/admin/tables"
@model PRN222_Restaurant.Pages.Admin.TablesModel
@{
    ViewData["Title"] = "Quản lý bàn ăn";
}

<div class="card bg-base-100 shadow-md">
    <div class="card-body">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <h2 class="card-title text-2xl">Quản lý bàn ăn</h2>
            <a href="/admin/tables/create" class="btn btn-primary mt-2 md:mt-0">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                        clip-rule="evenodd" />
                </svg>
                Thêm bàn ăn
            </a>
        </div>

        <!-- XÓA THANH TÌM KIẾM VÀ THANH TRẠNG THÁI  -->
        <!-- Table Status Legend -->
        <div class="flex items-center justify-center gap-8 mb-6">
            <div class="flex items-center">
                <div class="w-6 h-6 bg-green-500 rounded-md mr-2"></div>
                <span>Còn trống</span>
            </div>
            <div class="flex items-center">
                <div class="w-6 h-6 bg-yellow-400 rounded-md mr-2" style="background-color: #facc15;"></div>
                <span>Đã đặt</span>
            </div>
            <div class="flex items-center">
                <div class="w-6 h-6 bg-red-500 rounded-md mr-2"></div>
                <span>Đang sử dụng</span>
            </div>
        </div>

        <!-- Restaurant Area -->
        <div class="relative border-2 border-gray-400 rounded-lg p-6 bg-gray-50 mb-16">
            <!-- Restaurant Entrance -->
            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-white px-4 py-1 border border-gray-400 rounded-md text-sm font-medium">
                Entrance
            </div>
            <!-- Kitchen Area -->
            <div class="absolute -bottom-4 right-8 bg-white px-4 py-1 border border-gray-400 rounded-md text-sm font-medium">
                Kitchen
            </div>
            <!-- Bar Area -->
            <div class="absolute -left-4 top-1/2 transform -translate-y-1/2 bg-white px-4 py-1 border border-gray-400 rounded-md text-sm font-medium rotate-90">
                Bar
            </div>
            <!-- Tables Grid -->
            <div class="grid grid-cols-4 gap-4 mb-8">
                @foreach (var table in Model.Tables)
                {
                    string bgColor = table.Status == "Available"
                        ? "bg-green-500"
                        : table.Status == "Pending"
                            ? "bg-yellow-400"
                            : table.Status == "Reserved"
                                ? "bg-yellow-400"
                                : "bg-red-500";
                    string extraStyle = (table.Status == "Pending" || table.Status == "Reserved") ? "background-color: #facc15;" : "";
                    string opacity = table.Status != "Available" ? "opacity-60" : "";
                    <div class="flex flex-col items-center">
                        <div class="rounded-lg p-4 h-28 w-full text-center text-white shadow-md border-2 border-gray-600 @bgColor @opacity flex flex-col justify-between"
                             style="@extraStyle">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                            <div class="font-bold text-lg">Bàn @table.TableNumber</div>
                            <div>@table.Capacity người</div>
                        </div>
                        <div class="flex justify-center gap-2 mt-2">
                            <button type="button" class="btn btn-xs btn-square btn-warning !opacity-100"
                                onclick="openEditTableModal(@table.Id, @table.TableNumber, @table.Capacity, '@table.Status')">Sửa</button>
                            <button onclick="confirmDelete(@table.Id, '@table.TableNumber')" class="btn btn-xs btn-square btn-error !opacity-100">Xóa</button>
                        </div>
                    </div>
                }
            </div>
        </div>

        <!-- Pagination -->
        <div class="flex justify-between items-center mt-4">
            <div class="text-sm text-gray-600">
                Hiển thị @((Model.CurrentPage - 1) * Model.PageSize + 1) đến
                @(Math.Min(Model.CurrentPage * Model.PageSize, Model.TotalTables))
                trên tổng số @Model.TotalTables bàn
            </div>
            <div class="join">
                <button class="join-item btn btn-sm @(Model.CurrentPage == 1 ? "btn-disabled" : "")"
                    onclick="changePage(@(Model.CurrentPage - 1))">«</button>
                @for (int i = 1; i <= Model.TotalPages; i++)
                {
                    <button class="join-item btn btn-sm @(Model.CurrentPage == i ? "btn-active" : "")"
                        onclick="changePage(@i)">@i</button>
                }
                <button class="join-item btn btn-sm @(Model.CurrentPage == Model.TotalPages ? "btn-disabled" : "")"
                    onclick="changePage(@(Model.CurrentPage + 1))">»</button>
            </div>
            <div class="flex items-center">
                <span class="mr-2">Hiển thị:</span>
                <select id="pageSize" class="select select-bordered select-sm w-auto max-w-xs"
                    onchange="changePageSize(this)">
                    <option value="5" selected="@(Model.PageSize == 5)">5</option>
                    <option value="10" selected="@(Model.PageSize == 10)">10</option>
                    <option value="20" selected="@(Model.PageSize == 20)">20</option>
                    <option value="50" selected="@(Model.PageSize == 50)">50</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Delete confirmation modal -->
    <dialog id="delete-modal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg">Xác nhận xoá</h3>
            <p class="py-4">Bạn có chắc chắn muốn xóa sản phẩm <span id="delete-product-name" class="font-bold"></span>?
            </p>
            <div class="modal-action">
                <form method="post">
                    <input type="hidden" id="delete-product-id" name="tableId" value="" />
                    <button class="btn btn-error" type="submit" asp-page-handler="Delete">Xóa</button>
                    <button class="btn btn-ghost" type="button" onclick="closeDeleteModal()">Hủy</button>
                </form>
            </div>
        </div>
    </dialog>

    <!-- Edit Table Modal -->
    <dialog id="edit-table-modal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg">Cập nhật bàn ăn</h3>
            <form method="post" asp-page-handler="Edit">
                <input type="hidden" id="edit-table-id" name="editTable.Id" />
                <div class="form-control mb-2">
                    <label class="label">Số bàn</label>
                    <input type="number" id="edit-table-number" name="editTable.TableNumber"
                        class="input input-bordered w-full" required />
                </div>
                <div class="form-control mb-2">
                    <label class="label">Sức chứa</label>
                    <input type="number" id="edit-table-capacity" name="editTable.Capacity"
                        class="input input-bordered w-full" required />
                </div>
                <div class="form-control mb-2">
                    <!-- Đã xóa label và select trạng thái -->
                </div>
                <div class="modal-action">
                    <button class="btn btn-primary" type="submit">Lưu</button>
                    <button class="btn btn-ghost" type="button" onclick="closeEditTableModal()">Hủy</button>
                </div>
            </form>
        </div>
    </dialog>

    <!-- Add Table Modal -->
    <dialog id="add-table-modal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg">Thêm bàn ăn mới</h3>
            <form method="post" asp-page-handler="Add">
                <input type="hidden" name="page" value="@Model.CurrentPage" />
                <div class="form-control mb-2">
                    <label class="label">Số bàn</label>
                    <input type="number" name="newTable.TableNumber" class="input input-bordered w-full" required />
                </div>
                <div class="form-control mb-2">
                    <label class="label">Sức chứa</label>
                    <input type="number" name="newTable.Capacity" class="input input-bordered w-full" required />
                </div>
                <input type="hidden" name="newTable.Status" value="Available" />
                <div class="modal-action">
                    <button class="btn btn-primary" type="submit">Thêm</button>
                    <button class="btn btn-ghost" type="button" onclick="closeAddTableModal()">Hủy</button>
                </div>
            </form>
        </div>
    </dialog>

@section Scripts {
        <script>
            // Show toast notification if exists
            document.addEventListener('DOMContentLoaded', function () {
                @if (!string.IsNullOrEmpty(Model.SuccessMessage))
                    {
                        <text>showToast('@Model.SuccessMessage', 'success');</text>
                }
                    setTimeout(() => {
                        document.getElementById('loading-skeleton').classList.add('hidden');
                        document.getElementById('tables-table-container').classList.remove('hidden');
                    }, 500);
                setupFilters();
            });

            // Confirmation modal functions
            function confirmDelete(productId, productName) {
                document.getElementById('delete-product-id').value = productId;
                document.getElementById('delete-product-name').textContent = productName;
                document.getElementById('delete-modal').showModal();
            }
            function closeDeleteModal() {
                document.getElementById('delete-modal').close();
            }
            // Pagination function
            function changePage(page) {
                if (page < 1 || page > @Model.TotalPages) return;
                window.location.href = '/admin/tables?currentPage=' + page + '&pageSize=' + @Model.PageSize;
            }
            function changePageSize(select) {
                window.location.href = '/admin/tables?currentPage=1&pageSize=' + select.value;
            }
            // Setup filter functions
            function setupFilters() {
                const statusFilter = document.getElementById('statusFilter');
                const table = document.getElementById('tablesTable');
                if (!statusFilter || !table) return;
                statusFilter.addEventListener('change', function () {
                    const statusValue = statusFilter.value;
                    const rows = table.querySelectorAll('tbody tr');
                    rows.forEach(row => {
                        // Lấy status thực tế từ thuộc tính data-status hoặc text content
                        let statusCell = row.cells[3];
                        let status = statusCell.getAttribute('data-status') || statusCell.textContent;
                        status = status.trim();
                        if (!statusValue || status === statusValue) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                });
            }
            // Edit table modal functions
            function openEditTableModal(id, number, capacity, status) {
                document.getElementById('edit-table-id').value = id;
                document.getElementById('edit-table-number').value = number;
                document.getElementById('edit-table-capacity').value = capacity;
                document.getElementById('edit-table-modal').showModal();
            }
            function closeEditTableModal() {
                document.getElementById('edit-table-modal').close();
            }
            // Add table modal functions
            document.querySelector('a.btn.btn-primary').addEventListener('click', function (e) {
                e.preventDefault();
                document.getElementById('add-table-modal').showModal();
            });
            function closeAddTableModal() {
                document.getElementById('add-table-modal').close();
            }
        </script>
}
