namespace PRN222_Restaurant.Services.IService
{
    public interface IReservationSessionService
    {
        void SaveReservationData(int tableId, DateTime reservationDate, TimeSpan reservationTime, 
            int numberOfGuests, string note, string selectedItems);
        ReservationSessionData? GetReservationData();
        void ClearReservationData();
    }

    public class ReservationSessionData
    {
        public int TableId { get; set; }
        public DateTime ReservationDate { get; set; }
        public TimeSpan ReservationTime { get; set; }
        public int NumberOfGuests { get; set; }
        public string Note { get; set; } = string.Empty;
        public string SelectedItems { get; set; } = string.Empty;
    }
}
