@page
@using PRN222_Restaurant.Models
@using PRN222_Restaurant.Pages.BlazorComponent
@model IndexModel
@{
    ViewData["Title"] = "Home";
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-error mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>@TempData["ErrorMessage"]</span>
    </div>
}

<!-- Hero Section -->
<section class="relative h-screen flex items-center justify-center">
    <!-- Background Image with Overlay -->
    <div class="absolute inset-0 z-0">
        <img src="https://images.unsplash.com/photo-1551218808-94e220e084d2?q=80&w=2070" 
             alt="Fresh Seafood" 
             class="w-full h-full object-cover" />
        <div class="absolute inset-0 bg-gradient-to-r from-slate-900/90 to-slate-900/70"></div>
    </div>
    
    <!-- Content -->
    <div class="container mx-auto px-4 z-10 text-center">
        <h1 class="text-4xl md:text-6xl font-bold text-white mb-4 animate__animated animate__fadeInDown">
            Ocean Delights
        </h1>
        <p class="text-xl md:text-2xl text-slate-200 mb-8 md:max-w-2xl mx-auto animate__animated animate__fadeIn">
            Experience the freshest seafood dishes crafted with passion and expertise
        </p>
        <div class="flex flex-col sm:flex-row justify-center gap-4 animate__animated animate__fadeInUp">
            <a href="/reservation" class="btn btn-primary btn-lg">Reserve a Table</a>
            <a href="/menu" class="btn btn-outline btn-lg text-white hover:bg-white hover:text-primary">View Menu</a>
        </div>
    </div>
    
    <!-- Scroll Down Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
    </div>
</section>

<!-- About Section -->
<section class="py-16 md:py-24 bg-slate-50">
    <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row items-center gap-12">
            <div class="md:w-1/2">
                <img src="https://images.unsplash.com/photo-1630175860333-5131bcd619c8?q=80&w=2070" 
                     alt="Restaurant Interior" 
                     class="rounded-xl shadow-lg w-full h-auto object-cover" />
            </div>
            <div class="md:w-1/2">
                <h2 class="text-3xl font-bold text-slate-800 mb-6">Who We Are</h2>
                <p class="text-lg text-slate-600 mb-6">
                    Located on the beautiful coastline, Ocean Delights brings the bounty of the sea to your plate with the freshest catches and innovative preparations. Our chefs craft each dish with passion, blending traditional techniques with modern culinary artistry.
                </p>
                <p class="text-lg text-slate-600 mb-6">
                    We source our seafood ethically from local fishermen who practice sustainable fishing, ensuring that our ocean's resources remain plentiful for generations to come.
                </p>
                <a href="/about" class="btn btn-outline btn-primary">Learn More About Us</a>
            </div>
        </div>
    </div>
</section>

<!-- Featured Dishes Section -->
<section class="py-16 md:py-24 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-slate-800 mb-4">Featured Dishes</h2>
            <p class="text-lg text-slate-600 max-w-3xl mx-auto">
                Explore our chef's most celebrated creations, prepared with the freshest ingredients and served with flair
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Dish 1 -->
            <div class="card bg-base-100 shadow-xl">
                <figure class="h-64">
                    <img src="https://images.unsplash.com/photo-1559737558-2f5a35f4523b?q=80&w=1974" 
                         alt="Grilled Lobster" 
                         class="w-full h-full object-cover" />
                </figure>
                <div class="card-body">
                    <h3 class="card-title text-xl">Grilled Maine Lobster</h3>
                    <div class="flex items-center gap-2 mb-2">
                        <div class="badge badge-primary">Chef's Special</div>
                        <div class="badge badge-outline">Gluten Free</div>
                    </div>
                    <p class="text-slate-600">Tender lobster grilled to perfection, served with drawn butter and fresh herbs</p>
                    <div class="flex justify-between items-center mt-4">
                        <span class="text-xl font-bold text-primary">$42.99</span>
                        <a href="/menu" class="btn btn-sm btn-primary">Order Now</a>
                    </div>
                </div>
            </div>
            
            <!-- Dish 2 -->
            <div class="card bg-base-100 shadow-xl">
                <figure class="h-64">
                    <img src="https://images.unsplash.com/photo-1579871494447-9811cf80d66c?q=80&w=2070" 
                         alt="Seafood Paella" 
                         class="w-full h-full object-cover" />
                </figure>
                <div class="card-body">
                    <h3 class="card-title text-xl">Seafood Paella</h3>
                    <div class="flex items-center gap-2 mb-2">
                        <div class="badge badge-secondary">Popular</div>
                        <div class="badge badge-outline">Serves 2</div>
                    </div>
                    <p class="text-slate-600">Saffron-infused rice with mussels, clams, shrimp, and calamari, topped with fresh herbs</p>
                    <div class="flex justify-between items-center mt-4">
                        <span class="text-xl font-bold text-primary">$38.99</span>
                        <a href="/menu" class="btn btn-sm btn-primary">Order Now</a>
                    </div>
                </div>
            </div>
            
            <!-- Dish 3 -->
            <div class="card bg-base-100 shadow-xl">
                <figure class="h-64">
                    <img src="https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?q=80&w=2070" 
                         alt="Seared Scallops" 
                         class="w-full h-full object-cover" />
                </figure>
                <div class="card-body">
                    <h3 class="card-title text-xl">Seared Sea Scallops</h3>
                    <div class="flex items-center gap-2 mb-2">
                        <div class="badge badge-accent">New</div>
                        <div class="badge badge-outline">Keto Friendly</div>
                    </div>
                    <p class="text-slate-600">Jumbo scallops pan-seared to golden perfection, served with cauliflower purée and microgreens</p>
                    <div class="flex justify-between items-center mt-4">
                        <span class="text-xl font-bold text-primary">$36.99</span>
                        <a href="/menu" class="btn btn-sm btn-primary">Order Now</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-12">
            <a href="/menu" class="btn btn-lg btn-primary">View Full Menu</a>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="py-16 md:py-24 bg-slate-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-slate-800 mb-4">What Our Guests Say</h2>
            <p class="text-lg text-slate-600 max-w-3xl mx-auto">
                The experiences of our valued customers speak for themselves
            </p>
        </div>
        
        <div class="carousel w-full">
            <!-- Testimonial 1 -->
            <div id="testimonial1" class="carousel-item relative w-full">
                <div class="flex flex-col items-center justify-center w-full max-w-4xl mx-auto px-4">
                    <div class="avatar mb-6">
                        <div class="w-24 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                            <img src="https://i.pravatar.cc/150?img=32" alt="Customer" />
                        </div>
                    </div>
                    <div class="rating rating-md mb-4">
                        <input type="radio" name="rating-1" class="mask mask-star-2 bg-orange-400" checked />
                        <input type="radio" name="rating-1" class="mask mask-star-2 bg-orange-400" checked />
                        <input type="radio" name="rating-1" class="mask mask-star-2 bg-orange-400" checked />
                        <input type="radio" name="rating-1" class="mask mask-star-2 bg-orange-400" checked />
                        <input type="radio" name="rating-1" class="mask mask-star-2 bg-orange-400" checked />
                    </div>
                    <blockquote class="text-xl text-center text-slate-600 italic mb-6">
                        "The seafood at Ocean Delights is absolutely exceptional. Every bite was bursting with freshness and flavor. The lobster was the best I've ever had!"
                    </blockquote>
                    <p class="font-bold text-slate-800">Sarah Johnson</p>
                    <p class="text-slate-500">Boston, MA</p>
                </div>
                <div class="absolute flex justify-between transform -translate-y-1/2 left-5 right-5 top-1/2">
                    <a href="#testimonial3" class="btn btn-circle">❮</a> 
                    <a href="#testimonial2" class="btn btn-circle">❯</a>
                </div>
            </div>
            
            <!-- Testimonial 2 -->
            <div id="testimonial2" class="carousel-item relative w-full">
                <div class="flex flex-col items-center justify-center w-full max-w-4xl mx-auto px-4">
                    <div class="avatar mb-6">
                        <div class="w-24 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                            <img src="https://i.pravatar.cc/150?img=68" alt="Customer" />
                        </div>
                    </div>
                    <div class="rating rating-md mb-4">
                        <input type="radio" name="rating-2" class="mask mask-star-2 bg-orange-400" checked />
                        <input type="radio" name="rating-2" class="mask mask-star-2 bg-orange-400" checked />
                        <input type="radio" name="rating-2" class="mask mask-star-2 bg-orange-400" checked />
                        <input type="radio" name="rating-2" class="mask mask-star-2 bg-orange-400" checked />
                        <input type="radio" name="rating-2" class="mask mask-star-2 bg-orange-400" checked />
                    </div>
                    <blockquote class="text-xl text-center text-slate-600 italic mb-6">
                        "Not only was the food amazing, but the service and atmosphere were outstanding. I celebrated my anniversary here and they made it truly special."
                    </blockquote>
                    <p class="font-bold text-slate-800">Michael Thompson</p>
                    <p class="text-slate-500">New York, NY</p>
                </div>
                <div class="absolute flex justify-between transform -translate-y-1/2 left-5 right-5 top-1/2">
                    <a href="#testimonial1" class="btn btn-circle">❮</a> 
                    <a href="#testimonial3" class="btn btn-circle">❯</a>
                </div>
            </div>
            
            <!-- Testimonial 3 -->
            <div id="testimonial3" class="carousel-item relative w-full">
                <div class="flex flex-col items-center justify-center w-full max-w-4xl mx-auto px-4">
                    <div class="avatar mb-6">
                        <div class="w-24 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                            <img src="https://i.pravatar.cc/150?img=47" alt="Customer" />
                        </div>
                    </div>
                    <div class="rating rating-md mb-4">
                        <input type="radio" name="rating-3" class="mask mask-star-2 bg-orange-400" checked />
                        <input type="radio" name="rating-3" class="mask mask-star-2 bg-orange-400" checked />
                        <input type="radio" name="rating-3" class="mask mask-star-2 bg-orange-400" checked />
                        <input type="radio" name="rating-3" class="mask mask-star-2 bg-orange-400" checked />
                        <input type="radio" name="rating-3" class="mask mask-star-2 bg-orange-400" checked />
                    </div>
                    <blockquote class="text-xl text-center text-slate-600 italic mb-6">
                        "As a seafood enthusiast, I can confidently say that Ocean Delights offers the most authentic and flavorful seafood dishes I've experienced outside of coastal towns."
                    </blockquote>
                    <p class="font-bold text-slate-800">Emily Rodriguez</p>
                    <p class="text-slate-500">San Francisco, CA</p>
                </div>
                <div class="absolute flex justify-between transform -translate-y-1/2 left-5 right-5 top-1/2">
                    <a href="#testimonial2" class="btn btn-circle">❮</a> 
                    <a href="#testimonial1" class="btn btn-circle">❯</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Location and Hours Section -->
<section class="py-16 md:py-24 bg-white">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row gap-12">
            <div class="lg:w-1/2">
                <h2 class="text-3xl font-bold text-slate-800 mb-6">Find Us</h2>
                <div class="bg-slate-100 p-1 rounded-xl shadow-md h-96">
                    <iframe 
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3024.2219901290355!2d-74.00369368400567!3d40.71312937933185!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c25a23e28c1191%3A0x49f75d3281df052a!2s150%20Park%20Row%2C%20New%20York%2C%20NY%2010007%2C%20USA!5e0!3m2!1sen!2sus!4v1674988456960!5m2!1sen!2sus" 
                        class="w-full h-full rounded-lg" 
                        style="border:0;" 
                        allowfullscreen="" 
                        loading="lazy" 
                        referrerpolicy="no-referrer-when-downgrade">
                    </iframe>
                </div>
                <div class="mt-6">
                    <p class="flex items-center gap-2 text-lg text-slate-700 mb-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        150 Park Row, New York, NY 10007
                    </p>
                    <p class="flex items-center gap-2 text-lg text-slate-700 mb-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        (212) 555-1234
                    </p>
                    <p class="flex items-center gap-2 text-lg text-slate-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        <EMAIL>
                    </p>
                </div>
            </div>
            
            <div class="lg:w-1/2">
                <h2 class="text-3xl font-bold text-slate-800 mb-6">Hours of Operation</h2>
                <div class="overflow-x-auto">
                    <table class="table table-zebra w-full">
                        <tbody>
                            <tr>
                                <td class="font-medium">Monday</td>
                                <td>4:00 PM - 10:00 PM</td>
                            </tr>
                            <tr>
                                <td class="font-medium">Tuesday</td>
                                <td>4:00 PM - 10:00 PM</td>
                            </tr>
                            <tr>
                                <td class="font-medium">Wednesday</td>
                                <td>4:00 PM - 10:00 PM</td>
                            </tr>
                            <tr>
                                <td class="font-medium">Thursday</td>
                                <td>4:00 PM - 10:00 PM</td>
                            </tr>
                            <tr>
                                <td class="font-medium">Friday</td>
                                <td>12:00 PM - 11:00 PM</td>
                            </tr>
                            <tr>
                                <td class="font-medium">Saturday</td>
                                <td>12:00 PM - 11:00 PM</td>
                            </tr>
                            <tr>
                                <td class="font-medium">Sunday</td>
                                <td>12:00 PM - 9:00 PM</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-8 p-6 bg-primary/10 rounded-xl border border-primary/20">
                    <h3 class="text-2xl font-bold text-primary mb-4">Make a Reservation</h3>
                    <p class="text-lg text-slate-700 mb-6">
                        Reserve your table now and experience the finest seafood cuisine. For parties of 8 or more, please call us directly.
                    </p>
                    <a href="/reservation" class="btn btn-primary btn-lg w-full sm:w-auto">Reserve a Table</a>
                </div>
            </div>
        </div>
    </div>
</section>



<component type="typeof(PRN222_Restaurant.Pages.BlazorComponent.FeedbackPreview)" render-mode="ServerPrerendered" />




<!-- Newsletter Section -->
<section class="py-12 bg-primary text-white">
    <div class="container mx-auto px-4">
        <div class="flex flex-col md:flex-row items-center justify-between gap-8">
            <div>
                <h2 class="text-2xl font-bold mb-2">Subscribe to Our Newsletter</h2>
                <p class="text-white/80">Stay updated with our special offers and events</p>
            </div>
            <div class="w-full md:w-auto">
                <div class="join">
                    <input class="input join-item" placeholder="Your email address"/>
                    <button class="btn join-item bg-white text-primary hover:bg-white/90">Subscribe</button>
                </div>
            </div>
        </div>
    </div>
</section>






@{
    ViewData["Title"] = "Home page";
}