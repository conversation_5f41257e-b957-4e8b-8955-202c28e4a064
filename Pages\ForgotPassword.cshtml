﻿@page "/ForgotPassword"
@model ForgotPasswordModel
@{
    Layout = null;
    ViewData["Title"] = "Forgot Password";
}

<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - PRN222_Restaurant</title>
    <link rel="stylesheet" href="~/css/site.min.css" asp-append-version="true" />
</head>
<body class="bg-base-200 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full">
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h2 class="card-title text-2xl font-bold text-center mb-6">Forgot Password</h2>

                @if (!string.IsNullOrEmpty(Model.Message))
                {
                    <div class="alert alert-success">
                        <span>@Model.Message</span>
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                {
                    <div class="alert alert-error">
                        <span>@Model.ErrorMessage</span>
                    </div>
                }

                <form method="post">
                    <div class="form-control w-full mb-4">
                        <label class="label">
                            <span class="label-text">Email</span>
                        </label>
                        <input asp-for="Input.Email" class="input input-bordered w-full" type="email" placeholder="Enter your email" required />
                        <span asp-validation-for="Input.Email" class="text-error"></span>
                    </div>

                    <div class="form-control mt-6">
                        <button type="submit" class="btn btn-primary w-full">Send Verification Code</button>
                    </div>

                    <div class="text-center mt-4">
                        <a asp-page="/Login" class="text-sm text-blue-600 hover:underline">Back to Login</a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <partial name="_ValidationScriptsPartial" />
</body>
</html>
