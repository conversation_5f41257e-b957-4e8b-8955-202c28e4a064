@page "/admin/users"
@model PRN222_Restaurant.Pages.Admin.UsersModel
@{
    ViewData["Title"] = "Quản lý người dùng";
}

<div class="card bg-base-100 shadow-md">
    <div class="card-body">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <h2 class="card-title text-2xl">Quản lý người dùng</h2>
            <a href="/admin/users/create" class="btn btn-primary mt-2 md:mt-0">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                </svg>
                Thêm người dùng
            </a>
        </div>

        <!-- Search and filters -->
        <div class="mb-4">
            <div class="join w-full md:w-1/3">
                <input type="text" id="searchInput" data-search-table="usersTable" placeholder="Tìm kiếm người dùng..." class="input input-bordered join-item w-full" />
                <button class="btn join-item">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Loading skeleton (hidden when data is loaded) -->
        <div id="loading-skeleton" class="@(Model.Users.Any() ? "hidden" : "")">
            @for (int i = 0; i < 5; i++)
            {
                <div class="h-16 bg-base-200 animate-pulse rounded-md mb-2"></div>
            }
        </div>

        <!-- Users table -->
        <div class="overflow-x-auto" id="users-table-container" class="@(!Model.Users.Any() ? "hidden" : "")">
            <table id="usersTable" class="table table-zebra w-full">
                <thead>
                    <tr>
                        <th>STT</th>
                        <th>Aavatar</th>
                        <th>Email</th>
                        <th>Vai trò</th>
                        <th>Trạng thái</th>
                        <th>Hành động</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var user in Model.Users)
                    {
                        <tr>
                            <td>@user.Id</td>
                            <td>
                                <div class="flex items-center gap-3">
                                    <div class="avatar">
                                        <div class="mask mask-squircle w-8 h-8">
                                            <img src="https://ui-avatars.com/api/?name=@Uri.EscapeDataString(user.FullName)&background=random" alt="@user.FullName" />
                                        </div>
                                    </div>
                                    <div>
                                        <div class="font-bold">@user.FullName</div>
                                    </div>
                                </div>
                            </td>
                            <td>@user.Email</td>
                            <td>
                                <span class="badge @(user.Role == "Admin" ? "badge-error" : (user.Role == "Manager" ? "badge-warning" : "badge-info"))">
                                    @user.Role
                                </span>
                            </td>
                            <td>
                                @if (user.IsActive)
                                {
                                    <span class="badge badge-success">Hoạt động</span>
                                }
                                else
                                {
                                    <span class="badge badge-ghost">Không hoạt động</span>
                                }
                            </td>
                            <td>
                                <div class="flex gap-1">
                                    <a href="/admin/users/edit/@user.Id" class="btn btn-xs btn-square btn-warning">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                        </svg>
                                    </a>
                                    <button onclick="confirmDelete(@user.Id, '@user.FullName')" class="btn btn-xs btn-square btn-error">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>


        <!-- Pagination controls -->
        <div class="flex justify-between items-center mt-4">
            <div class="text-sm text-gray-600">
                Hiển thị @((Model.UsersResult.Page - 1) * Model.UsersResult.PageSize + 1) đến
                @(Math.Min(Model.UsersResult.Page * Model.UsersResult.PageSize, Model.UsersResult.TotalCount))
                trên tổng số @Model.UsersResult.TotalCount người dùng
            </div>

            <div class="join">
                @if (Model.UsersResult.HasPreviousPage)
                {
                    <a href="/admin/users?currentPage=@(Model.UsersResult.Page - 1)&pageSize=@Model.UsersResult.PageSize"
                       class="join-item btn btn-outline">«</a>
                }
                else
                {
                    <button class="join-item btn btn-outline" disabled>«</button>
                }

                @for (int i = Math.Max(1, Model.UsersResult.Page - 2); i <= Math.Min(Model.UsersResult.TotalPages, Math.Max(Model.UsersResult.Page + 2, 5)); i++)
                {
                    if (i == Model.UsersResult.Page)
                    {
                        <button class="join-item btn btn-active">@i</button>
                    }
                    else
                    {
                        <a href="/admin/users?currentPage=@i&pageSize=@Model.UsersResult.PageSize"
                           class="join-item btn btn-outline">@i</a>
                    }
                }

                @if (Model.UsersResult.HasNextPage)
                {
                    <a href="/admin/users?currentPage=@(Model.UsersResult.Page + 1)&pageSize=@Model.UsersResult.PageSize"
                       class="join-item btn btn-outline">»</a>
                }
                else
                {
                    <button class="join-item btn btn-outline" disabled>»</button>
                }
            </div>

            <div class="flex items-center">
                <span class="mr-2">Hiển thị:</span>
                <select id="pageSize" class="select select-bordered select-sm w-auto max-w-xs"
                        onchange="location.href='/admin/users?currentPage=1&pageSize=' + this.value">
                    @{
                        bool isSelected5 = Model.UsersResult.PageSize == 5;
                        bool isSelected10 = Model.UsersResult.PageSize == 10;
                        bool isSelected20 = Model.UsersResult.PageSize == 20;
                        bool isSelected50 = Model.UsersResult.PageSize == 50;
                    }
                    <option value="5" selected="@isSelected5">5</option>
                    <option value="10" selected="@isSelected10">10</option>
                    <option value="20" selected="@isSelected20">20</option>
                    <option value="50" selected="@isSelected50">50</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Delete confirmation modal -->
<dialog id="delete-modal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg">Xác nhận xoá</h3>
        <p class="py-4">Bạn có chắc chắn muốn xóa người dùng <span id="delete-user-name" class="font-bold"></span>?</p>
        <div class="modal-action">
            <form method="post">
                <input type="hidden" id="delete-user-id" name="userId" value="" />
                <button class="btn btn-error" type="submit" asp-page-handler="Delete">Xóa</button>
                <button class="btn btn-ghost" type="button" onclick="closeDeleteModal()">Hủy</button>
            </form>
        </div>
    </div>
</dialog>

@section Scripts {
    <script>
        // Show toast notification if exists
        document.addEventListener('DOMContentLoaded', function() {
            @if (!string.IsNullOrEmpty(Model.SuccessMessage))
            {
                        <text>showToast('@Model.SuccessMessage', 'success');</text>
            }

            // Hide loading skeleton and show table
            setTimeout(() => {
                document.getElementById('loading-skeleton').classList.add('hidden');
                document.getElementById('users-table-container').classList.remove('hidden');
            }, 500);
        });

        // Confirmation modal functions
        function confirmDelete(userId, userName) {
            document.getElementById('delete-user-id').value = userId;
            document.getElementById('delete-user-name').textContent = userName;
            document.getElementById('delete-modal').showModal();
        }

        function closeDeleteModal() {
            document.getElementById('delete-modal').close();
        }

        // Pagination function
        function changePage(page) {
            window.location.href = '/admin/users?page=' + page;
        }
    </script>
}