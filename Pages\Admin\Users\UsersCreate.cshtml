﻿@page "/admin/users/create"
@model PRN222_Restaurant.Pages.Admin.UsersCreateModel
@{
    ViewData["Title"] = "Thêm người dùng";
    var successMessage = TempData["SuccessMessage"] as string;
}

<div class="card bg-base-100 shadow-md max-w-lg mx-auto mt-8">
    <div class="card-body">
        <h2 class="card-title text-2xl mb-6">Thêm người dùng mới</h2>

        @* ✅ Thông báo khi thêm thành công *@
        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success shadow-lg mb-4">
                <div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-6 w-6" fill="none"
                         viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M9 12l2 2l4-4m6 2a9 9 0 1 1-18 0a9 9 0 0 1 18 0z" />
                    </svg>
                    <span>@successMessage</span>
                </div>
            </div>
        }

        <form method="post" class="space-y-4">
            <div>
                <label class="block mb-1 font-semibold" asp-for="User.FullName">Họ và tên</label>
                <input asp-for="User.FullName" class="input input-bordered w-full" placeholder="Nhập họ tên" />
                <span asp-validation-for="User.FullName" class="text-error text-sm"></span>
            </div>

            <div>
                <label class="block mb-1 font-semibold" asp-for="User.Email">Email</label>
                <input asp-for="User.Email" type="email" class="input input-bordered w-full" placeholder="<EMAIL>" />
                <span asp-validation-for="User.Email" class="text-error text-sm"></span>
            </div>

            <div>
                <label class="block mb-1 font-semibold" asp-for="User.Role">Vai trò</label>
                <select asp-for="User.Role" class="select select-bordered w-full" aria-label="Vai trò">
                    <option value="">-- Chọn vai trò --</option>
                    <option>Admin</option>
                    <option>Manager</option>
                    <option>User</option>
                </select>
                <span asp-validation-for="User.Role" class="text-error text-sm"></span>
            </div>

            <div class="form-control">
                <label class="cursor-pointer label">
                    <span class="label-text">Kích hoạt người dùng</span>
                    <input asp-for="User.IsActive" type="checkbox" class="checkbox checkbox-primary" />
                </label>
            </div>

            <div class="flex justify-between mt-6">
                <a href="/admin/users" class="btn btn-outline">Hủy</a>
                <button type="submit" class="btn btn-primary">Lưu</button>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
