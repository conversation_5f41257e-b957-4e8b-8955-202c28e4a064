@page "/admin/products"
@model PRN222_Restaurant.Pages.Admin.ProductsModel
@{
    ViewData["Title"] = "Quản lý sản phẩm";
}

<div class="card bg-base-100 shadow-md">
    <div class="card-body">
        <!-- Header -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <h2 class="card-title text-2xl mb-2 md:mb-0">Quản lý sản phẩm</h2>
            <div class="flex mt-2 md:mt-0">
                <a href="/admin/products/createcate" class="btn btn-secondary mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                    </svg>
                    Thêm danh mục
                </a>
                <a href="/admin/products/create" class="btn btn-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                    </svg>
                    Thêm sản phẩm
                </a>
            </div>
        </div>

        <!-- Filter Form -->
        <form method="get" class="mb-6 grid grid-cols-1 lg:grid-cols-4 gap-4">
            <div class="lg:col-span-2">
                <input type="text" name="SearchTerm" value="@Model.SearchTerm" placeholder="Tìm kiếm sản phẩm..." class="input input-bordered w-full" />
            </div>
            <div>
                <select name="SelectedCategory" class="select select-bordered w-full" onchange="this.form.submit()">
                    <option value="">Tất cả danh mục</option>
                    @foreach (var category in Model.Categories)
                    {
                        <option value="@category.Name" selected="@(category.Name == Model.SelectedCategory)">
                            @category.Name
                        </option>
                    }
                </select>
            </div>
            <div>
                <select name="SelectedStatus" class="select select-bordered w-full" onchange="this.form.submit()">
                    <option value="">Tất cả trạng thái</option>
                    @foreach (var status in Model.Statuses)
                    {
                        <option value="@status" selected="@(status == Model.SelectedStatus)">
                            @status
                        </option>
                    }
                </select>
            </div>
        </form>

        <!-- Thông báo -->
        @if (!string.IsNullOrEmpty(Model.SuccessMessage))
        {
            <div class="alert alert-success mb-4">
                @Model.SuccessMessage
            </div>
        }

        <!-- Bảng sản phẩm -->
        @if (Model.Products.Any())
        {
            <div class="overflow-x-auto">
                <table class="table table-zebra w-full">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Ảnh</th>
                            <th>Tên sản phẩm</th>
                            <th>Danh mục</th>
                            <th>Giá</th>
                            <th>Trạng thái</th>
                            <th>Hành động</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var product in Model.Products)
                        {
                            <tr>
                                <td>@product.Id</td>
                                <td>
                                    <div class="avatar">
                                        <div class="mask mask-squircle w-12 h-12">
                                            <img src="@product.ImageUrl" alt="@product.Name" />
                                        </div>
                                    </div>
                                </td>
                                <td class="font-medium">@product.Name</td>
                                <td>@product.Category.Name</td>
                                <td>@product.Price.ToString("N0") đ</td>
                                <td>
                                    @if (product.Status == Models.MenuItemStatus.Available)
                                    {
                                        <span class="badge badge-success">@product.Status</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-warning">@product.Status</span>
                                    }
                                </td>
                                <td>
                                    <div class="flex gap-1">
                                        <a href="/admin/products/edit/@product.Id" class="btn btn-xs btn-square btn-warning">
                                            ✎
                                        </a>
                                        <form method="post" asp-page-handler="Delete" class="inline">
                                            <input type="hidden" name="productId" value="@product.Id" />
                                            <input type="hidden" name="page" value="@Model.CurrentPage" />
                                            <input type="hidden" name="SearchTerm" value="@Model.SearchTerm" />
                                            <input type="hidden" name="SelectedCategory" value="@Model.SelectedCategory" />
                                            <input type="hidden" name="SelectedStatus" value="@Model.SelectedStatus" />
                                            <button type="submit" class="btn btn-xs btn-square btn-error" onclick="return confirm('Bạn có chắc muốn xóa sản phẩm này?');">
                                                🗑️
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4 flex justify-between items-center flex-wrap">
                <div class="text-sm text-gray-500 mb-2 md:mb-0">
                    Hiển thị <span class="font-medium">@Model.FromRecord</span> đến <span class="font-medium">@Model.ToRecord</span>
                    trong tổng số <span class="font-medium">@Model.TotalProducts</span> sản phẩm
                </div>
                <div class="join">

                    <!-- Nút « -->
                    <a asp-page="/Admin/Products"
                       asp-route-pageNumber="@(Model.CurrentPage - 1)"
                       asp-route-SearchTerm="@Model.SearchTerm"
                       asp-route-SelectedCategory="@Model.SelectedCategory"
                       asp-route-SelectedStatus="@Model.SelectedStatus"
                       class="join-item btn btn-sm @(Model.CurrentPage == 1 ? "btn-disabled" : "")">
                        «
                    </a>

                    <!-- Các nút số trang -->
                    @for (int i = 1; i <= Model.TotalPages; i++)
                    {
                        <a asp-page="/Admin/Products"
                           asp-route-pageNumber="@i"
                           asp-route-SearchTerm="@Model.SearchTerm"
                           asp-route-SelectedCategory="@Model.SelectedCategory"
                           asp-route-SelectedStatus="@Model.SelectedStatus"
                           class="join-item btn btn-sm @(Model.CurrentPage == i ? "btn-active" : "")">
                            @i
                        </a>
                    }

                    <!-- Nút » -->
                    <a asp-page="/Admin/Products"
                       asp-route-pageNumber="@(Model.CurrentPage + 1)"
                       asp-route-SearchTerm="@Model.SearchTerm"
                       asp-route-SelectedCategory="@Model.SelectedCategory"
                       asp-route-SelectedStatus="@Model.SelectedStatus"
                       class="join-item btn btn-sm @(Model.CurrentPage == Model.TotalPages ? "btn-disabled" : "")">
                        »
                    </a>
                </div>
            </div>

        }
        else
        {
            <p class="text-center text-gray-500 py-8">Không có sản phẩm nào được tìm thấy.</p>
        }
    </div>
</div>
