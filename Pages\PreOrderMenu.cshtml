@page "/preordermenu"
@model PRN222_Restaurant.Pages.PreOrderMenuModel
@{
    ViewData["Title"] = "Order Details";
}

<div class="container mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">Your Order Details</h1>

        @if (!string.IsNullOrEmpty(Model.StatusMessage))
        {
            <div class="alert alert-warning mb-6">
                @Model.StatusMessage
            </div>
        }

        @if (Model.CurrentOrder != null)
        {
            <!-- Order Summary -->
            <div class="bg-base-200 p-6 rounded-lg mb-8">
                <h2 class="text-xl font-semibold mb-4">Reservation Details</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p><span class="font-medium">Date:</span> @Model.CurrentOrder.ReservationTime?.ToString("MMMM dd, yyyy")</p>
                        <p><span class="font-medium">Time:</span> @Model.CurrentOrder.ReservationTime?.ToString("HH:mm")</p>
                    </div>
                    <div>
                        <p><span class="font-medium">Table:</span> @Model.CurrentOrder.Table?.TableNumber</p>
                        <p><span class="font-medium">Guests:</span> @Model.CurrentOrder.NumberOfGuests</p>
                    </div>
                </div>
                @if (!string.IsNullOrEmpty(Model.CurrentOrder.Note))
                {
                    <div class="mt-4">
                        <p><span class="font-medium">Note:</span> @Model.CurrentOrder.Note</p>
                    </div>
                }
            </div>

            <!-- Order Items -->
            <div class="bg-white p-6 rounded-lg shadow-md mb-8">
                <h2 class="text-xl font-semibold mb-4">Order Items</h2>

                @if (Model.CurrentOrder.OrderItems.Any())
                {
                    <div class="space-y-4">
                        @foreach (var orderItem in Model.CurrentOrder.OrderItems)
                        {
                            <div class="flex items-center justify-between p-4 border rounded-lg">
                                <div class="flex items-center space-x-4">
                                    <img src="@orderItem.MenuItem.ImageUrl" alt="@orderItem.MenuItem.Name" class="w-16 h-16 object-cover rounded" />
                                    <div>
                                        <h3 class="font-semibold">@orderItem.MenuItem.Name</h3>
                                        <p class="text-gray-600">@orderItem.MenuItem.Description</p>
                                        <p class="text-primary font-bold">$@orderItem.UnitPrice.ToString("F2") each</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-lg font-semibold">Quantity: @orderItem.Quantity</p>
                                    <p class="text-xl font-bold text-primary">$@((orderItem.UnitPrice * orderItem.Quantity).ToString("F2"))</p>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <p class="text-gray-500 text-center py-8">No items in your order yet.</p>
                    <div class="text-center">
                        <a href="/reservation" class="btn btn-primary">Go back to add items</a>
                    </div>
                }
            </div>

            <!-- Order Summary -->
            <div class="bg-primary text-primary-content p-6 rounded-lg shadow-md mb-8">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold">Total Items: @Model.TotalItems</h3>
                        <h3 class="text-2xl font-bold">Total: $@Model.TotalPrice.ToString("F2")</h3>
                    </div>
                    <div class="flex gap-4">
                        <a href="/reservation" class="btn btn-outline btn-lg">
                            Edit Order
                        </a>
                        <form method="post" class="contents">
                            <button type="submit" class="btn btn-secondary btn-lg" @(Model.TotalItems == 0 ? "disabled" : "")>
                                Proceed to Confirmation
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="text-center py-12">
                <p class="text-gray-500 mb-4">No order found.</p>
                <a href="/reservation" class="btn btn-primary">Create New Reservation</a>
            </div>
        }
    </div>
</div>

