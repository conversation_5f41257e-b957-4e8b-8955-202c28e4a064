using PRN222_Restaurant.Services.IService;
using System.Text.Json;

namespace PRN222_Restaurant.Services.Service
{
    public class ReservationSessionService : IReservationSessionService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public ReservationSessionService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public void SaveReservationData(int tableId, DateTime reservationDate, TimeSpan reservationTime, 
            int numberOfGuests, string note, string selectedItems)
        {
            var data = new ReservationSessionData
            {
                TableId = tableId,
                ReservationDate = reservationDate,
                ReservationTime = reservationTime,
                NumberOfGuests = numberOfGuests,
                Note = note,
                SelectedItems = selectedItems
            };

            var json = JsonSerializer.Serialize(data);
            _httpContextAccessor.HttpContext?.Session.SetString("PendingReservation", json);
        }

        public ReservationSessionData? GetReservationData()
        {
            var json = _httpContextAccessor.HttpContext?.Session.GetString("PendingReservation");
            if (string.IsNullOrEmpty(json))
                return null;

            try
            {
                return JsonSerializer.Deserialize<ReservationSessionData>(json);
            }
            catch
            {
                return null;
            }
        }

        public void ClearReservationData()
        {
            _httpContextAccessor.HttpContext?.Session.Remove("PendingReservation");
        }
    }
}
