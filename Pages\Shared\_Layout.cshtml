﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - PRN222_Restaurant Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Outfit', sans-serif;
        }
    </style>
    <link rel="stylesheet" href="~/css/site.min.css" asp-append-version="true"/>
    <base href="~/" />
    <component type="typeof(Microsoft.AspNetCore.Components.Web.HeadOutlet)" render-mode="ServerPrerendered" />
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous" defer></script>
    <script src="~/js/dashboard.js" asp-append-version="true" defer></script>

</head>
<body>
    @using PRN222_Restaurant.Helper
    @inject IHttpContextAccessor HttpContextAccessor
    @using System.Security.Claims

    @{
        var user = HttpContextAccessor.HttpContext?.User;
        if (!AuthHelper.IsStaffOrAdmin(user))
        {
            Context.Response.Redirect("/");
        }
    }

    @{
        var _user = HttpContextAccessor.HttpContext?.User;
        var userEmail = _user?.Identity?.IsAuthenticated == true
        ? _user.FindFirst(ClaimTypes.Email)?.Value ?? "User":null;
    }



    <div class="drawer lg:drawer-open">
        <!-- Mobile menu toggle -->
        <input id="drawer-toggle" type="checkbox" class="drawer-toggle" />
        
        <div class="drawer-content flex flex-col">
            <!-- Topbar / Header -->
            <header class="bg-base-100 shadow-md">
                <div class="navbar bg-base-100">
                    <div class="flex-none lg:hidden">
                        <label for="drawer-toggle" class="btn btn-square btn-ghost drawer-button">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="inline-block w-5 h-5 stroke-current">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </label>
                    </div>
                    <div class="flex-1">
                        <span class="font-bold text-xl lg:hidden">PRN222 Restaurant</span>
                    </div>
                    <div class="flex gap-3 items-center">
                        <!-- Admin Chat Panel Button -->
                        <a href="/admin/chat" class="btn btn-ghost btn-circle relative" title="Customer Chat Panel">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                            </svg>
                            <!-- Notification badge for new customer messages -->
                            <span id="adminChatNotificationBadge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">
                                <span id="adminChatNotificationCount">0</span>
                            </span>
                        </a>

                        <!-- Dark Mode Toggle -->
                        <button id="theme-toggle" class="btn btn-ghost btn-circle">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 theme-light-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 theme-dark-icon hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                            </svg>
                        </button>

                        <!-- Show User Name -->

                        <div class="font-semibold bg-red-300 px-3 py-1 rounded">@userEmail</div>

                    </div>
                </div>
            </header>



            <!-- Page content -->
            <main class="flex-1 p-4 md:p-6 overflow-y-auto bg-base-200">
                <!-- Toast/Alert container for notifications -->
                <div id="toast-container" class="toast toast-top toast-end"></div>
                
                @RenderBody()
            </main>
        </div>
        
        <!-- Sidebar -->
        <div class="drawer-side">
            <label for="drawer-toggle" class="drawer-overlay"></label>
            <aside class="bg-base-100 w-64 min-h-screen border-r">
                <div class="p-4 border-b">
                    <h1 class="text-xl font-bold">PRN222 Restaurant</h1>
                </div>                <ul class="menu p-4 text-base-content">
                    <li>
                        <a href="/admin/dashboard" class="@(ViewContext.RouteData.Values["page"]?.ToString()?.Contains("Dashboard") == true ? "active" : "")">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="/admin/users" class="@(ViewContext.RouteData.Values["page"]?.ToString()?.Contains("Users") == true ? "active" : "")">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                            Quản lý người dùng
                        </a>
                    </li>
                    <li>
                        <a href="/admin/products" class="@(ViewContext.RouteData.Values["page"]?.ToString()?.Contains("Products") == true ? "active" : "")">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                            Quản lý sản phẩm
                        </a>
                    </li>
                    <li>
                        <a href="/admin/stats" class="@(ViewContext.RouteData.Values["page"]?.ToString()?.Contains("Stats") == true ? "active" : "")">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            Thống kê
                        </a>
                    </li>      
                    <li>
                        <a href="/admin/feedback" class="@(ViewContext.RouteData.Values["page"]?.ToString()?.Contains("Stats") == true ? "active" : "")">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            Feedback
                        </a>
                    </li>

                    <li>
                        <a href="/admin/chat" class="@(ViewContext.RouteData.Values["page"]?.ToString()?.Contains("Admin/Chat") == true ? "active" : "")">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                            </svg>
                            Chat Management
                        </a>
                    </li>
                    <li>
                        <a href="/admin/orders" class="@(ViewContext.RouteData.Values["page"]?.ToString()?.Contains("Orders") == true ? "active" : "")">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                        Quản lý đơn hàng
                        </a>
                    </li>        
                    <li>
                        <a href="/admin/tables" class="@(ViewContext.RouteData.Values["page"]?.ToString()?.Contains("Tables") == true ? "active" : "")">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                            </svg>
                            Quản lý bàn ăn

                        </a>
                    </li>
                    <li class="border-t mt-4 pt-4">
                        <a href="/admin/logout">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                            </svg>
                            Đăng xuất
                        </a>
                    </li>
                </ul>
            </aside>
        </div>
    </div>

    <script src="_framework/blazor.server.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.1/signalr.min.js"></script>

    <script>
        // Admin chat notification system
        let adminChatConnection;

        async function initializeAdminChatNotifications() {
            adminChatConnection = new signalR.HubConnectionBuilder()
                .withUrl("/chatHub")
                .build();

            // Handle new customer messages
            adminChatConnection.on("NewCustomerMessage", function (data) {
                showAdminChatNotification();

                // Show toast notification
                console.log(`New message from ${data.CustomerName}: ${data.Message}`);
            });

            try {
                await adminChatConnection.start();
                console.log("Admin Chat Notifications Connected");
            } catch (err) {
                console.error("Admin Chat Notifications Error: ", err);
            }
        }

        function showAdminChatNotification() {
            const badge = document.getElementById('adminChatNotificationBadge');
            const count = document.getElementById('adminChatNotificationCount');

            if (badge && count) {
                let currentCount = parseInt(count.textContent) || 0;
                count.textContent = currentCount + 1;
                badge.classList.remove('hidden');
            }
        }

        function hideAdminChatNotification() {
            const badge = document.getElementById('adminChatNotificationBadge');
            const count = document.getElementById('adminChatNotificationCount');

            if (badge && count) {
                count.textContent = '0';
                badge.classList.add('hidden');
            }
        }

        // Hide notification when admin visits chat page
        if (window.location.pathname === '/admin/chat') {
            hideAdminChatNotification();
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeAdminChatNotifications();
        });
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>