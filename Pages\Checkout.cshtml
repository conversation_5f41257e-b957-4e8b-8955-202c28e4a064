@page "/checkout"
@model PRN222_Restaurant.Pages.CheckoutModel
@{
    ViewData["Title"] = "Complete Your Payment";
}

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-gray-800 mb-2">Complete Your Payment</h1>
                <p class="text-gray-600">Review your order and proceed with secure payment</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Order Summary -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
                            <h2 class="text-xl font-semibold text-white flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                                Order Summary
                            </h2>
                        </div>
                        <div class="p-6">

                            @if (Model.OrderItems != null && Model.OrderItems.Any())
                            {
                                <div class="space-y-4">
                                    @foreach (var item in Model.OrderItems)
                                    {
                                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                            <div class="flex-1">
                                                <h3 class="font-semibold text-gray-800">@item.Name</h3>
                                                <p class="text-sm text-gray-600">$@item.Price.ToString("F2") each</p>
                                            </div>
                                            <div class="text-center mx-4">
                                                <span class="inline-flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold">
                                                    @item.Quantity
                                                </span>
                                            </div>
                                            <div class="text-right">
                                                <p class="font-semibold text-gray-800">$@item.Subtotal.ToString("F2")</p>
                                            </div>
                                        </div>
                                    }
                                </div>

                                <!-- Order Totals -->
                                <div class="mt-6 pt-6 border-t border-gray-200">
                                    <div class="space-y-3">
                                        <div class="flex justify-between text-gray-600">
                                            <span>Subtotal:</span>
                                            <span>$@Model.Subtotal.ToString("F2")</span>
                                        </div>
                                        <div class="flex justify-between text-gray-600">
                                            <span>Tax (10%):</span>
                                            <span>$@Model.Tax.ToString("F2")</span>
                                        </div>
                                        <div class="flex justify-between text-xl font-bold text-gray-800 pt-3 border-t border-gray-200">
                                            <span>Total:</span>
                                            <span class="text-blue-600">$@Model.Total.ToString("F2")</span>
                                        </div>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-8">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                    </svg>
                                    <p class="text-gray-500">No items in your order</p>
                                </div>
                            }
                    </div>
                </div>
            </div>

                <!-- Payment Section -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                        <div class="bg-gradient-to-r from-green-600 to-emerald-600 px-6 py-4">
                            <h2 class="text-xl font-semibold text-white flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                </svg>
                                Secure Payment
                            </h2>
                        </div>
                        <div class="p-6">
                            <!-- Payment Amount Display -->
                            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 mb-6">
                                <div class="text-center">
                                    <p class="text-gray-600 text-sm mb-2">Total Amount</p>
                                    <p class="text-3xl font-bold text-blue-600">${@Model.Total.ToString("F2")}</p>
                                </div>
                            </div>

                            <form method="post" class="space-y-4">
                                <input type="hidden" asp-for="PaymentInfo.OrderId" value="@Model.OrderId" />
                                <input type="hidden" asp-for="PaymentInfo.Amount" value="@Model.Total" />
                                <input type="hidden" asp-for="PaymentInfo.OrderDescription" value="Payment for reservation #@Model.OrderId" />
                                <input type="hidden" asp-for="PaymentInfo.OrderType" value="PreOrder" />
                                <input type="hidden" asp-for="PaymentInfo.Name" value="@(User.Identity.IsAuthenticated? User.Identity.Name : "Guest")" />

                                <button type="submit" class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-4 px-6 rounded-xl transition duration-300 transform hover:scale-105 shadow-lg">
                                    <div class="flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                        </svg>
                                        <span class="text-lg">Pay with VNPay</span>
                                    </div>
                                </button>
                            </form>

                            <!-- Security Features -->
                            <div class="mt-6 p-4 bg-green-50 rounded-lg">
                                <div class="flex items-center mb-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                    </svg>
                                    <h3 class="font-semibold text-green-800">Secure Payment</h3>
                                </div>
                                <ul class="space-y-2 text-sm text-green-700">
                                    <li class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        SSL encrypted connection
                                    </li>
                                    <li class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        PCI DSS compliant
                                    </li>
                                    <li class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        Money-back guarantee
                                    </li>
                                </ul>
                            </div>

                            <!-- Payment Instructions -->
                            <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                                <h3 class="font-semibold text-blue-800 mb-3 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    Payment Process
                                </h3>
                                <ol class="list-decimal list-inside space-y-2 text-sm text-blue-700">
                                    <li>Click "Pay with VNPay" button</li>
                                    <li>You'll be redirected to VNPay gateway</li>
                                    <li>Select your payment method</li>
                                    <li>Complete the secure payment</li>
                                    <li>Return to confirmation page</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>